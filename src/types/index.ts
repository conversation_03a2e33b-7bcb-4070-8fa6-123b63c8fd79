export interface UserProfile {
  age: number;
  gender: 'male' | 'female' | 'other';
  height: number; // in cm
  weight: number; // in kg
  activityLevel: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | 'extremely_active';
  goal: 'lose_weight' | 'maintain_weight' | 'gain_weight' | 'build_muscle';
  bodyType?: 'ectomorph' | 'mesomorph' | 'endomorph';
  medicalConditions?: string[];
  allergies?: string[];
  dietaryPreferences?: string[];
}

export interface WeightEntry {
  date: string;
  weight: number;
  notes?: string;
}

export interface DietPlan {
  dailyCalories: number;
  macros: {
    protein: number;
    carbs: number;
    fat: number;
  };
  meals: Meal[];
  tips: string[];
}

export interface Meal {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  ingredients: string[];
  instructions: string[];
}

export interface WorkoutPlan {
  name: string;
  duration: number; // in minutes
  exercises: Exercise[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface Exercise {
  name: string;
  sets: number;
  reps: string;
  duration?: number; // for cardio
  calories: number;
  instructions: string[];
}
