import React, { useState } from 'react';
import { geminiService } from './geminiService.js';

// Modern Design System
const theme = {
  colors: {
    primary: { 500: '#0ea5e9', 600: '#0284c7', 700: '#0369a1' },
    success: { 500: '#22c55e', 600: '#16a34a' },
    warning: { 500: '#f59e0b', 600: '#d97706' },
    error: { 500: '#ef4444', 600: '#dc2626' },
    gray: { 50: '#f9fafb', 100: '#f3f4f6', 200: '#e5e7eb', 600: '#4b5563', 900: '#111827' }
  },
  gradients: {
    primary: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
    success: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
    warm: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
  },
  shadows: {
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  }
};

interface UserProfile {
  age: number;
  gender: 'male' | 'female' | 'other';
  height: number;
  weight: number;
  activityLevel: string;
  goal: string;
  bodyType?: string;
}

interface FoodEntry {
  id: string;
  date: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  meal: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

function App() {
  const [currentPage, setCurrentPage] = useState<'welcome' | 'profile' | 'dashboard' | 'calories'>('welcome');
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [foodEntries, setFoodEntries] = useState<FoodEntry[]>([]);
  const [dietPlan, setDietPlan] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const addFoodEntry = (food: Omit<FoodEntry, 'id'>) => {
    const newEntry: FoodEntry = {
      ...food,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
    };
    setFoodEntries(prev => [...prev, newEntry]);
  };

  const getTodayCalories = () => {
    const today = new Date().toISOString().split('T')[0];
    return foodEntries
      .filter(entry => entry.date === today)
      .reduce((sum, entry) => sum + entry.calories, 0);
  };

  // Welcome Page
  if (currentPage === 'welcome') {
    return (
      <div style={{
        minHeight: '100vh',
        background: theme.gradients.primary,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <div style={{
          maxWidth: '500px',
          width: '100%',
          backgroundColor: 'white',
          borderRadius: '24px',
          padding: '40px',
          boxShadow: theme.shadows.xl,
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '64px', marginBottom: '16px' }}>🏋️</div>
          <h1 style={{
            color: theme.colors.gray[900],
            margin: '0 0 8px 0',
            fontSize: '32px',
            fontWeight: '700'
          }}>
            FitAI
          </h1>
          <p style={{
            color: theme.colors.gray[600],
            margin: '0 0 32px 0',
            fontSize: '18px'
          }}>
            Your AI-Powered Fitness Companion
          </p>

          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '16px',
            marginBottom: '32px'
          }}>
            {[
              { icon: '📸', title: 'Smart Food Tracking', desc: 'AI photo recognition' },
              { icon: '🎯', title: 'Personalized Plans', desc: 'Based on body type' },
              { icon: '💪', title: 'Custom Workouts', desc: 'Tailored exercises' },
              { icon: '📊', title: 'Progress Insights', desc: 'Smart adjustments' }
            ].map((feature, index) => (
              <div key={index} style={{
                backgroundColor: theme.colors.gray[50],
                padding: '20px',
                borderRadius: '16px',
                border: `1px solid ${theme.colors.gray[200]}`
              }}>
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>{feature.icon}</div>
                <h3 style={{
                  fontSize: '14px',
                  fontWeight: '600',
                  color: theme.colors.gray[900],
                  margin: '0 0 4px 0'
                }}>
                  {feature.title}
                </h3>
                <p style={{
                  fontSize: '12px',
                  color: theme.colors.gray[600],
                  margin: '0'
                }}>
                  {feature.desc}
                </p>
              </div>
            ))}
          </div>

          <button
            style={{
              background: theme.gradients.primary,
              color: 'white',
              padding: '16px 32px',
              border: 'none',
              borderRadius: '16px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: '600',
              width: '100%',
              boxShadow: theme.shadows.lg
            }}
            onClick={() => setCurrentPage('profile')}
          >
            Get Started - Create Your Profile
          </button>
        </div>
      </div>
    );
  }

  // Profile Creation Page
  if (currentPage === 'profile') {
    return (
      <div style={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.colors.gray[50]} 0%, ${theme.colors.gray[100]} 100%)`,
        padding: '20px',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        <div style={{ maxWidth: '500px', margin: '0 auto' }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '24px',
            padding: '32px',
            marginBottom: '24px',
            boxShadow: theme.shadows.lg,
            textAlign: 'center'
          }}>
            <button
              onClick={() => setCurrentPage('welcome')}
              style={{
                position: 'absolute',
                left: '32px',
                top: '32px',
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: theme.colors.gray[600]
              }}
            >
              ←
            </button>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>👤</div>
            <h1 style={{
              color: theme.colors.gray[900],
              margin: '0 0 8px 0',
              fontSize: '28px',
              fontWeight: '700'
            }}>
              Create Your Profile
            </h1>
            <p style={{
              color: theme.colors.gray[600],
              margin: '0',
              fontSize: '16px'
            }}>
              Tell us about yourself for personalized AI recommendations
            </p>
          </div>

          <div style={{
            backgroundColor: 'white',
            borderRadius: '24px',
            padding: '32px',
            boxShadow: theme.shadows.lg
          }}>
            <form onSubmit={async (e) => {
              e.preventDefault();
              setIsLoading(true);

              const formData = new FormData(e.target as HTMLFormElement);
              const newProfile: UserProfile = {
                age: parseInt(formData.get('age') as string),
                gender: formData.get('gender') as 'male' | 'female' | 'other',
                height: parseInt(formData.get('height') as string),
                weight: parseFloat(formData.get('weight') as string),
                activityLevel: formData.get('activityLevel') as string,
                goal: formData.get('goal') as string,
              };

              try {
                const bodyType = await geminiService.analyzeBodyType(newProfile);
                newProfile.bodyType = bodyType;
                const dietPlanResult = await geminiService.generateDietPlan(newProfile, []);
                setDietPlan(dietPlanResult);
              } catch (error) {
                console.error('Error with AI analysis:', error);
                newProfile.bodyType = 'mesomorph';
              }

              setProfile(newProfile);
              setIsLoading(false);
              setCurrentPage('dashboard');
            }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: theme.colors.gray[700],
                    fontSize: '14px'
                  }}>
                    Age
                  </label>
                  <input
                    type="number"
                    name="age"
                    required
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: `2px solid ${theme.colors.gray[200]}`,
                      fontSize: '16px',
                      outline: 'none'
                    }}
                  />
                </div>
                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: theme.colors.gray[700],
                    fontSize: '14px'
                  }}>
                    Gender
                  </label>
                  <select
                    name="gender"
                    required
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: `2px solid ${theme.colors.gray[200]}`,
                      fontSize: '16px',
                      backgroundColor: 'white',
                      outline: 'none'
                    }}
                  >
                    <option value="">Select gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: theme.colors.gray[700],
                    fontSize: '14px'
                  }}>
                    Height (cm)
                  </label>
                  <input
                    type="number"
                    name="height"
                    required
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: `2px solid ${theme.colors.gray[200]}`,
                      fontSize: '16px',
                      outline: 'none'
                    }}
                  />
                </div>
                <div>
                  <label style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: '600',
                    color: theme.colors.gray[700],
                    fontSize: '14px'
                  }}>
                    Weight (kg)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    name="weight"
                    required
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      borderRadius: '12px',
                      border: `2px solid ${theme.colors.gray[200]}`,
                      fontSize: '16px',
                      outline: 'none'
                    }}
                  />
                </div>
              </div>

              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: theme.colors.gray[700],
                  fontSize: '14px'
                }}>
                  Activity Level
                </label>
                <select
                  name="activityLevel"
                  required
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    borderRadius: '12px',
                    border: `2px solid ${theme.colors.gray[200]}`,
                    fontSize: '16px',
                    backgroundColor: 'white',
                    outline: 'none'
                  }}
                >
                  <option value="">Select activity level</option>
                  <option value="sedentary">Sedentary (little or no exercise)</option>
                  <option value="lightly_active">Lightly Active (light exercise 1-3 days/week)</option>
                  <option value="moderately_active">Moderately Active (moderate exercise 3-5 days/week)</option>
                  <option value="very_active">Very Active (hard exercise 6-7 days/week)</option>
                </select>
              </div>

              <div style={{ marginBottom: '32px' }}>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: theme.colors.gray[700],
                  fontSize: '14px'
                }}>
                  Goal
                </label>
                <select
                  name="goal"
                  required
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    borderRadius: '12px',
                    border: `2px solid ${theme.colors.gray[200]}`,
                    fontSize: '16px',
                    backgroundColor: 'white',
                    outline: 'none'
                  }}
                >
                  <option value="">Select your goal</option>
                  <option value="lose_weight">Lose Weight</option>
                  <option value="maintain_weight">Maintain Weight</option>
                  <option value="gain_weight">Gain Weight</option>
                  <option value="build_muscle">Build Muscle</option>
                </select>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                style={{
                  background: isLoading ? theme.colors.gray[400] : theme.gradients.primary,
                  color: 'white',
                  padding: '16px 32px',
                  border: 'none',
                  borderRadius: '16px',
                  cursor: isLoading ? 'not-allowed' : 'pointer',
                  fontSize: '16px',
                  fontWeight: '600',
                  width: '100%',
                  boxShadow: theme.shadows.lg
                }}
              >
                {isLoading ? '🤖 AI Analyzing Your Profile...' : '✨ Create My AI-Powered Profile'}
              </button>
            </form>
          </div>
        </div>
      </div>
    );
  }

  // Dashboard Page
  if (currentPage === 'dashboard') {
    return (
      <div style={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.colors.gray[50]} 0%, ${theme.colors.gray[100]} 100%)`,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}>
        {/* Header */}
        <div style={{
          background: 'white',
          padding: '20px',
          boxShadow: theme.shadows.md,
          borderBottom: `1px solid ${theme.colors.gray[200]}`
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{ fontSize: '32px' }}>🏋️</div>
              <h1 style={{
                color: theme.colors.gray[900],
                margin: '0',
                fontSize: '24px',
                fontWeight: '700'
              }}>
                FitAI Dashboard
              </h1>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <span style={{
                color: theme.colors.gray[600],
                fontSize: '14px'
              }}>
                Welcome, {profile?.gender === 'male' ? 'Sir' : profile?.gender === 'female' ? 'Ma\'am' : 'Friend'}! 👋
              </span>
            </div>
          </div>
        </div>

        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px' }}>
          {/* Stats Cards */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '20px', marginBottom: '32px' }}>
            <div style={{
              background: 'white',
              borderRadius: '20px',
              padding: '24px',
              boxShadow: theme.shadows.lg,
              border: `1px solid ${theme.colors.gray[200]}`
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{ fontSize: '24px' }}>📊</div>
                <h3 style={{
                  margin: '0',
                  color: theme.colors.gray[900],
                  fontSize: '18px',
                  fontWeight: '600'
                }}>
                  Your Stats
                </h3>
              </div>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                <div>
                  <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[600], fontWeight: '500' }}>AGE</p>
                  <p style={{ margin: '0', fontSize: '20px', fontWeight: '700', color: theme.colors.gray[900] }}>{profile?.age}</p>
                </div>
                <div>
                  <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[600], fontWeight: '500' }}>HEIGHT</p>
                  <p style={{ margin: '0', fontSize: '20px', fontWeight: '700', color: theme.colors.gray[900] }}>{profile?.height} cm</p>
                </div>
                <div>
                  <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[600], fontWeight: '500' }}>WEIGHT</p>
                  <p style={{ margin: '0', fontSize: '20px', fontWeight: '700', color: theme.colors.gray[900] }}>{profile?.weight} kg</p>
                </div>
                <div>
                  <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[600], fontWeight: '500' }}>BMI</p>
                  <p style={{ margin: '0', fontSize: '20px', fontWeight: '700', color: theme.colors.primary[600] }}>
                    {profile ? (profile.weight / ((profile.height / 100) ** 2)).toFixed(1) : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            <div style={{
              background: 'white',
              borderRadius: '20px',
              padding: '24px',
              boxShadow: theme.shadows.lg,
              border: `1px solid ${theme.colors.gray[200]}`
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{ fontSize: '24px' }}>🎯</div>
                <h3 style={{
                  margin: '0',
                  color: theme.colors.gray[900],
                  fontSize: '18px',
                  fontWeight: '600'
                }}>
                  Your Goals
                </h3>
              </div>
              <div style={{ display: 'grid', gap: '12px' }}>
                <div>
                  <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[600], fontWeight: '500' }}>PRIMARY GOAL</p>
                  <p style={{ margin: '0', fontSize: '16px', fontWeight: '600', color: theme.colors.gray[900], textTransform: 'capitalize' }}>
                    {profile?.goal?.replace('_', ' ')}
                  </p>
                </div>
                <div>
                  <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[600], fontWeight: '500' }}>ACTIVITY LEVEL</p>
                  <p style={{ margin: '0', fontSize: '16px', fontWeight: '600', color: theme.colors.gray[900], textTransform: 'capitalize' }}>
                    {profile?.activityLevel?.replace('_', ' ')}
                  </p>
                </div>
                <div>
                  <p style={{ margin: '0 0 4px 0', fontSize: '12px', color: theme.colors.gray[600], fontWeight: '500' }}>BODY TYPE</p>
                  <p style={{ margin: '0', fontSize: '16px', fontWeight: '600', color: theme.colors.success[600], textTransform: 'capitalize' }}>
                    {profile?.bodyType}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Cards */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '20px' }}>
            <div style={{
              background: 'white',
              borderRadius: '20px',
              padding: '24px',
              boxShadow: theme.shadows.lg,
              border: `1px solid ${theme.colors.gray[200]}`,
              cursor: 'pointer',
              transition: 'transform 0.2s ease'
            }}
            onClick={() => setCurrentPage('calories')}
            onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-4px)'}
            onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{ fontSize: '32px' }}>📱</div>
                <h3 style={{
                  margin: '0',
                  color: theme.colors.primary[600],
                  fontSize: '18px',
                  fontWeight: '600'
                }}>
                  Calorie Tracker
                </h3>
              </div>
              <p style={{ color: theme.colors.gray[600], margin: '0 0 12px 0' }}>
                Track your daily food intake with AI-powered food recognition
              </p>
              <p style={{ fontSize: '14px', color: theme.colors.gray[600], margin: '0 0 16px 0' }}>
                Today: {getTodayCalories()} / {dietPlan?.dailyCalories || 2000} cal
              </p>
              <div style={{
                background: theme.gradients.primary,
                color: 'white',
                padding: '12px 20px',
                borderRadius: '12px',
                textAlign: 'center',
                fontSize: '14px',
                fontWeight: '600'
              }}>
                Track Food →
              </div>
            </div>

            <div style={{
              background: 'white',
              borderRadius: '20px',
              padding: '24px',
              boxShadow: theme.shadows.lg,
              border: `1px solid ${theme.colors.gray[200]}`
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{ fontSize: '32px' }}>💪</div>
                <h3 style={{
                  margin: '0',
                  color: theme.colors.warning[600],
                  fontSize: '18px',
                  fontWeight: '600'
                }}>
                  Workout Plans
                </h3>
              </div>
              <p style={{ color: theme.colors.gray[600], margin: '0 0 12px 0' }}>
                Get personalized workout recommendations based on your goals
              </p>
              <p style={{ fontSize: '14px', color: theme.colors.gray[600], margin: '0 0 16px 0' }}>
                Coming Soon...
              </p>
              <div style={{
                background: theme.colors.gray[200],
                color: theme.colors.gray[600],
                padding: '12px 20px',
                borderRadius: '12px',
                textAlign: 'center',
                fontSize: '14px',
                fontWeight: '600'
              }}>
                Coming Soon
              </div>
            </div>

            <div style={{
              background: 'white',
              borderRadius: '20px',
              padding: '24px',
              boxShadow: theme.shadows.lg,
              border: `1px solid ${theme.colors.gray[200]}`
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{ fontSize: '32px' }}>🍽️</div>
                <h3 style={{
                  margin: '0',
                  color: theme.colors.success[600],
                  fontSize: '18px',
                  fontWeight: '600'
                }}>
                  Diet Plans
                </h3>
              </div>
              <p style={{ color: theme.colors.gray[600], margin: '0 0 12px 0' }}>
                AI-generated meal plans tailored to your body type and goals
              </p>
              <p style={{ fontSize: '14px', color: theme.colors.gray[600], margin: '0 0 16px 0' }}>
                {dietPlan ? `${dietPlan.meals?.length || 0} meals planned` : 'Generating...'}
              </p>
              <div style={{
                background: theme.colors.gray[200],
                color: theme.colors.gray[600],
                padding: '12px 20px',
                borderRadius: '12px',
                textAlign: 'center',
                fontSize: '14px',
                fontWeight: '600'
              }}>
                Coming Soon
              </div>
            </div>
          </div>

          {/* Reset Button */}
          <div style={{ textAlign: 'center', marginTop: '32px' }}>
            <button
              style={{
                background: theme.gradients.warm,
                color: 'white',
                padding: '12px 24px',
                border: 'none',
                borderRadius: '12px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                boxShadow: theme.shadows.md
              }}
              onClick={() => {
                setProfile(null);
                setCurrentPage('welcome');
              }}
            >
              🔄 Reset Profile
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Calorie Tracker Page - Cal AI Style
  if (currentPage === 'calories') {
    const today = new Date().toISOString().split('T')[0];
    const todayFoods = foodEntries.filter(entry => entry.date === today);
    const todayCalories = todayFoods.reduce((sum, entry) => sum + entry.calories, 0);
    const todayProtein = todayFoods.reduce((sum, entry) => sum + entry.protein, 0);
    const todayCarbs = todayFoods.reduce((sum, entry) => sum + entry.carbs, 0);
    const todayFat = todayFoods.reduce((sum, entry) => sum + entry.fat, 0);

    const targetCalories = dietPlan?.dailyCalories || 2000;
    const targetProtein = dietPlan?.macros?.protein || 150;
    const targetCarbs = dietPlan?.macros?.carbs || 200;
    const targetFat = dietPlan?.macros?.fat || 65;

    const mealGroups = {
      breakfast: todayFoods.filter(f => f.meal === 'breakfast'),
      lunch: todayFoods.filter(f => f.meal === 'lunch'),
      dinner: todayFoods.filter(f => f.meal === 'dinner'),
      snack: todayFoods.filter(f => f.meal === 'snack')
    };

    return (
      <div style={{
        padding: '20px',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        maxWidth: '400px',
        margin: '0 auto',
        backgroundColor: '#f8fafc',
        minHeight: '100vh'
      }}>
        {/* Header */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <button
            onClick={() => setCurrentPage('dashboard')}
            style={{ backgroundColor: 'transparent', border: 'none', fontSize: '24px', cursor: 'pointer' }}
          >
            ←
          </button>
          <h1 style={{ color: '#333', margin: '0', fontSize: '20px' }}>Today</h1>
          <div style={{ width: '24px' }}></div>
        </div>

        {/* Calorie Ring - Cal AI Style */}
        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <div style={{ position: 'relative', display: 'inline-block' }}>
            <svg width="200" height="200" style={{ transform: 'rotate(-90deg)' }}>
              <circle
                cx="100"
                cy="100"
                r="80"
                fill="none"
                stroke="#e5e7eb"
                strokeWidth="12"
              />
              <circle
                cx="100"
                cy="100"
                r="80"
                fill="none"
                stroke="#10b981"
                strokeWidth="12"
                strokeDasharray={`${2 * Math.PI * 80}`}
                strokeDashoffset={`${2 * Math.PI * 80 * (1 - Math.min(todayCalories / targetCalories, 1))}`}
                strokeLinecap="round"
              />
            </svg>
            <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', textAlign: 'center' }}>
              <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#333' }}>{todayCalories}</div>
              <div style={{ fontSize: '14px', color: '#666' }}>of {targetCalories}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>calories</div>
            </div>
          </div>
        </div>

        {/* Macro Breakdown */}
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '10px', marginBottom: '30px' }}>
          <div style={{ textAlign: 'center', backgroundColor: 'white', padding: '15px', borderRadius: '12px' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#3b82f6' }}>{todayProtein.toFixed(0)}g</div>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Protein</div>
            <div style={{ backgroundColor: '#e5e7eb', height: '4px', borderRadius: '2px' }}>
              <div style={{ backgroundColor: '#3b82f6', height: '100%', borderRadius: '2px', width: `${Math.min((todayProtein / targetProtein) * 100, 100)}%` }}></div>
            </div>
          </div>
          <div style={{ textAlign: 'center', backgroundColor: 'white', padding: '15px', borderRadius: '12px' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#f59e0b' }}>{todayCarbs.toFixed(0)}g</div>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Carbs</div>
            <div style={{ backgroundColor: '#e5e7eb', height: '4px', borderRadius: '2px' }}>
              <div style={{ backgroundColor: '#f59e0b', height: '100%', borderRadius: '2px', width: `${Math.min((todayCarbs / targetCarbs) * 100, 100)}%` }}></div>
            </div>
          </div>
          <div style={{ textAlign: 'center', backgroundColor: 'white', padding: '15px', borderRadius: '12px' }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#ef4444' }}>{todayFat.toFixed(0)}g</div>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Fat</div>
            <div style={{ backgroundColor: '#e5e7eb', height: '4px', borderRadius: '2px' }}>
              <div style={{ backgroundColor: '#ef4444', height: '100%', borderRadius: '2px', width: `${Math.min((todayFat / targetFat) * 100, 100)}%` }}></div>
            </div>
          </div>
        </div>

        {/* Add Food Button - Cal AI Style */}
        <div style={{ marginBottom: '30px' }}>
          <label style={{
            display: 'block',
            backgroundColor: '#10b981',
            color: 'white',
            padding: '15px',
            borderRadius: '12px',
            textAlign: 'center',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}>
            📸 Snap a photo to add food
            <input
              type="file"
              accept="image/*"
              capture="environment"
              style={{ display: 'none' }}
              onChange={async (e) => {
                const file = e.target.files?.[0];
                if (!file) return;

                setIsLoading(true);
                const reader = new FileReader();
                reader.onload = async (event) => {
                  const base64 = event.target?.result as string;
                  const base64Data = base64.split(',')[1];

                  try {
                    const analysis = await geminiService.analyzeFoodImage(base64Data);

                    const confirmed = confirm(`
🤖 AI detected: ${analysis.food}
📊 Estimated: ${analysis.calories} calories

Macros:
• Protein: ${analysis.macros.protein}g
• Carbs: ${analysis.macros.carbs}g
• Fat: ${analysis.macros.fat}g

Click OK to add, or Cancel to adjust manually.
                    `);

                    if (confirmed) {
                      const hour = new Date().getHours();
                      let mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack' = 'snack';
                      if (hour >= 6 && hour < 11) mealType = 'breakfast';
                      else if (hour >= 11 && hour < 16) mealType = 'lunch';
                      else if (hour >= 16 && hour < 22) mealType = 'dinner';

                      addFoodEntry({
                        date: today,
                        name: analysis.food,
                        calories: analysis.calories,
                        protein: analysis.macros.protein,
                        carbs: analysis.macros.carbs,
                        fat: analysis.macros.fat,
                        meal: mealType
                      });
                    }
                  } catch (error) {
                    console.error('Error:', error);
                    alert('🤖 AI had trouble analyzing this image. Try a clearer photo or add manually.');
                  }
                  setIsLoading(false);
                };
                reader.readAsDataURL(file);
              }}
            />
          </label>
          {isLoading && (
            <div style={{ textAlign: 'center', marginTop: '10px', color: '#666' }}>
              <div style={{ fontSize: '20px', marginBottom: '5px' }}>🤖</div>
              <div>AI analyzing your food...</div>
            </div>
          )}
        </div>

        {/* Meals by Time - Cal AI Style */}
        {Object.entries(mealGroups).map(([mealType, foods]) => {
          const mealCalories = foods.reduce((sum, food) => sum + food.calories, 0);
          const mealEmojis = { breakfast: '🌅', lunch: '☀️', dinner: '🌙', snack: '🍎' };

          return (
            <div key={mealType} style={{ marginBottom: '20px' }}>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '15px',
                border: foods.length > 0 ? '2px solid #10b981' : '1px solid #e5e7eb'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ fontSize: '20px' }}>{mealEmojis[mealType as keyof typeof mealEmojis]}</span>
                    <span style={{ fontSize: '16px', fontWeight: 'bold', textTransform: 'capitalize' }}>{mealType}</span>
                  </div>
                  <span style={{ fontSize: '14px', color: '#666', fontWeight: 'bold' }}>{mealCalories} cal</span>
                </div>

                {foods.length === 0 ? (
                  <button
                    onClick={() => {
                      const foodName = prompt(`Add food to ${mealType}:`);
                      if (foodName) {
                        const calories = parseInt(prompt('Estimated calories:') || '100');
                        addFoodEntry({
                          date: today,
                          name: foodName,
                          calories,
                          protein: Math.round(calories * 0.15 / 4),
                          carbs: Math.round(calories * 0.5 / 4),
                          fat: Math.round(calories * 0.35 / 9),
                          meal: mealType as 'breakfast' | 'lunch' | 'dinner' | 'snack'
                        });
                      }
                    }}
                    style={{
                      width: '100%',
                      padding: '10px',
                      backgroundColor: '#f3f4f6',
                      border: '1px dashed #d1d5db',
                      borderRadius: '8px',
                      color: '#666',
                      cursor: 'pointer'
                    }}
                  >
                    + Add {mealType}
                  </button>
                ) : (
                  <div style={{ display: 'grid', gap: '8px' }}>
                    {foods.map(food => (
                      <div key={food.id} style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '8px',
                        backgroundColor: '#f8fafc',
                        borderRadius: '6px'
                      }}>
                        <div>
                          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>{food.name}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            P: {food.protein}g • C: {food.carbs}g • F: {food.fat}g
                          </div>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <span style={{ fontSize: '14px', fontWeight: 'bold' }}>{food.calories}</span>
                          <button
                            onClick={() => setFoodEntries(prev => prev.filter(f => f.id !== food.id))}
                            style={{
                              backgroundColor: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              padding: '4px 8px',
                              fontSize: '12px',
                              cursor: 'pointer'
                            }}
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}

        {/* Quick Add Suggestions */}
        <div style={{ marginTop: '20px' }}>
          <h3 style={{ fontSize: '16px', marginBottom: '10px' }}>Quick Add</h3>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px' }}>
            {[
              { name: 'Water (8oz)', calories: 0 },
              { name: 'Coffee (black)', calories: 5 },
              { name: 'Apple (medium)', calories: 95 },
              { name: 'Banana (medium)', calories: 105 }
            ].map(item => (
              <button
                key={item.name}
                onClick={() => {
                  addFoodEntry({
                    date: today,
                    name: item.name,
                    calories: item.calories,
                    protein: 0,
                    carbs: item.calories / 4,
                    fat: 0,
                    meal: 'snack'
                  });
                }}
                style={{
                  padding: '8px',
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                {item.name}
              </button>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return <div>App is working! Current page: {currentPage}</div>;
}

export default App;
