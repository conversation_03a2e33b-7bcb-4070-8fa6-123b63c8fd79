import React, { useState } from 'react';
import { useUserStore } from './store/userStore';
import { useNutritionStore } from './store/nutritionStore';
import ProfileSetup from './components/ProfileSetup';
import Dashboard from './components/Dashboard';
import CalorieTracker from './components/CalorieTracker';

function App() {
  const [currentPage, setCurrentPage] = useState<'welcome' | 'profile' | 'dashboard' | 'calories'>('welcome');
  const { profile, isProfileComplete } = useUserStore();
  const { currentDietPlan } = useNutritionStore();

  // Determine which page to show based on profile completion
  React.useEffect(() => {
    if (!isProfileComplete) {
      setCurrentPage('welcome');
    } else if (isProfileComplete && currentPage === 'welcome') {
      setCurrentPage('dashboard');
    }
  }, [isProfileComplete, currentPage]);

  // Welcome Page
  if (currentPage === 'welcome' && !isProfileComplete) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="text-6xl mb-4">🏋️</div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">FitAI</h1>
          <p className="text-gray-600 mb-8">Your AI-Powered Fitness Companion</p>

          <div className="grid grid-cols-2 gap-4 mb-8">
            {[
              { icon: '📸', title: 'Smart Food Tracking', desc: 'AI photo recognition' },
              { icon: '🎯', title: 'Personalized Plans', desc: 'Based on body type' },
              { icon: '💪', title: 'Custom Workouts', desc: 'Tailored exercises' },
              { icon: '📊', title: 'Progress Insights', desc: 'Smart adjustments' }
            ].map((feature, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg border">
                <div className="text-2xl mb-2">{feature.icon}</div>
                <h3 className="text-sm font-semibold text-gray-900 mb-1">{feature.title}</h3>
                <p className="text-xs text-gray-600">{feature.desc}</p>
              </div>
            ))}
          </div>

          <button
            className="btn btn-primary w-full"
            onClick={() => setCurrentPage('profile')}
          >
            Get Started - Create Your Profile
          </button>
        </div>
      </div>
    );
  }

  // Profile Creation Page
  if (currentPage === 'profile') {
    return <ProfileSetup />;
  }

  // Dashboard Page
  if (currentPage === 'dashboard') {
    return (
      <div>
        <Dashboard />
        <div className="fixed bottom-4 right-4">
          <button
            className="btn btn-primary rounded-full p-4 shadow-lg"
            onClick={() => setCurrentPage('calories')}
          >
            📱 Track Food
          </button>
        </div>
      </div>
    );
  }

  // Calorie Tracker Page - Cal AI Style
  if (currentPage === 'calories') {
    return (
      <div>
        <div className="fixed top-4 left-4 z-50">
          <button
            className="btn btn-secondary rounded-full p-2"
            onClick={() => setCurrentPage('dashboard')}
          >
            ← Back
          </button>
        </div>
        <CalorieTracker />
      </div>
    );
  }

  return <div>App is working! Current page: {currentPage}</div>;
}

export default App;
