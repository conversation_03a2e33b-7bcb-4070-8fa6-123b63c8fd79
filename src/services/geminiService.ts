import { GoogleGenerativeAI } from '@google/generative-ai';
import type { UserProfile, WeightEntry, DietPlan, Meal, WorkoutPlan, Exercise } from '../types/index';

const API_KEY = 'AIzaSyCQz5-VAVuj03vXnaOx8YLsLTWb_mptUvQ';

const genAI = new GoogleGenerativeAI(API_KEY);

// Re-export types for convenience
export type { UserProfile, WeightEntry, DietPlan, Meal, WorkoutPlan, Exercise };

class GeminiService {
  private model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
  private visionModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  async analyzeBodyType(userProfile: Partial<UserProfile>): Promise<string> {
    const prompt = `
    Based on the following user information, determine their body type (ectomorph, mesomorph, or endomorph):
    - Age: ${userProfile.age}
    - Gender: ${userProfile.gender}
    - Height: ${userProfile.height}cm
    - Weight: ${userProfile.weight}kg
    - Activity Level: ${userProfile.activityLevel}

    Provide a detailed analysis and return only one of: ectomorph, mesomorph, or endomorph
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text().toLowerCase();

      if (text.includes('ectomorph')) return 'ectomorph';
      if (text.includes('mesomorph')) return 'mesomorph';
      if (text.includes('endomorph')) return 'endomorph';

      return 'mesomorph'; // default
    } catch (error) {
      console.error('Error analyzing body type:', error);
      return 'mesomorph';
    }
  }

  async generateDietPlan(userProfile: UserProfile, weightHistory: WeightEntry[]): Promise<DietPlan> {
    const recentWeightTrend = this.analyzeWeightTrend(weightHistory);

    const prompt = `
    Create a personalized diet plan for a user with the following profile:
    - Age: ${userProfile.age}
    - Gender: ${userProfile.gender}
    - Height: ${userProfile.height}cm
    - Current Weight: ${userProfile.weight}kg
    - Body Type: ${userProfile.bodyType}
    - Activity Level: ${userProfile.activityLevel}
    - Goal: ${userProfile.goal}
    - Weight Trend: ${recentWeightTrend}
    - Medical Conditions: ${userProfile.medicalConditions?.join(', ') || 'None'}
    - Allergies: ${userProfile.allergies?.join(', ') || 'None'}
    - Dietary Preferences: ${userProfile.dietaryPreferences?.join(', ') || 'None'}

    Return a JSON object with the following structure:
    {
      "dailyCalories": number,
      "macros": {
        "protein": number (in grams),
        "carbs": number (in grams),
        "fat": number (in grams)
      },
      "meals": [
        {
          "name": string,
          "calories": number,
          "protein": number,
          "carbs": number,
          "fat": number,
          "ingredients": [string],
          "instructions": [string]
        }
      ],
      "tips": [string]
    }

    Provide 4-5 meals (breakfast, lunch, dinner, and 1-2 snacks). Make sure the total calories and macros add up correctly.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error generating diet plan:', error);
      return this.getDefaultDietPlan(userProfile);
    }
  }

  async generateWorkoutPlan(userProfile: UserProfile): Promise<WorkoutPlan> {
    const prompt = `
    Create a personalized workout plan for a user with the following profile:
    - Age: ${userProfile.age}
    - Gender: ${userProfile.gender}
    - Body Type: ${userProfile.bodyType}
    - Activity Level: ${userProfile.activityLevel}
    - Goal: ${userProfile.goal}

    Return a JSON object with the following structure:
    {
      "name": string,
      "duration": number (in minutes),
      "exercises": [
        {
          "name": string,
          "sets": number,
          "reps": string,
          "duration": number (optional, for cardio),
          "calories": number (estimated calories burned),
          "instructions": [string]
        }
      ],
      "difficulty": "beginner" | "intermediate" | "advanced"
    }

    Provide 6-8 exercises suitable for their goals and fitness level.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error generating workout plan:', error);
      return this.getDefaultWorkoutPlan();
    }
  }

  async analyzeFoodImage(imageBase64: string): Promise<{ food: string; calories: number; macros: { protein: number; carbs: number; fat: number } }> {
    const prompt = `
    Analyze this food image and provide nutritional information.
    Return a JSON object with:
    {
      "food": string (name of the food),
      "calories": number (estimated calories),
      "macros": {
        "protein": number (in grams),
        "carbs": number (in grams),
        "fat": number (in grams)
      }
    }
    `;

    try {
      const result = await this.visionModel.generateContent([
        prompt,
        {
          inlineData: {
            data: imageBase64,
            mimeType: 'image/jpeg'
          }
        }
      ]);

      const response = await result.response;
      const text = response.text();

      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error analyzing food image:', error);
      return {
        food: 'Unknown Food',
        calories: 200,
        macros: { protein: 10, carbs: 20, fat: 8 }
      };
    }
  }

  private analyzeWeightTrend(weightHistory: WeightEntry[]): string {
    if (weightHistory.length < 2) return 'insufficient_data';

    const recent = weightHistory.slice(-4); // Last 4 entries
    const trend = recent[recent.length - 1].weight - recent[0].weight;

    if (trend > 1) return 'gaining';
    if (trend < -1) return 'losing';
    return 'stable';
  }

  private getDefaultDietPlan(userProfile: UserProfile): DietPlan {
    const baseCalories = this.calculateBMR(userProfile) * this.getActivityMultiplier(userProfile.activityLevel);

    return {
      dailyCalories: Math.round(baseCalories),
      macros: {
        protein: Math.round(userProfile.weight * 1.6),
        carbs: Math.round(baseCalories * 0.45 / 4),
        fat: Math.round(baseCalories * 0.25 / 9)
      },
      meals: [
        {
          name: 'Breakfast',
          calories: Math.round(baseCalories * 0.25),
          protein: 20,
          carbs: 40,
          fat: 15,
          ingredients: ['Oatmeal', 'Banana', 'Almonds'],
          instructions: ['Cook oatmeal', 'Add sliced banana', 'Top with almonds']
        }
      ],
      tips: ['Drink plenty of water', 'Eat regular meals', 'Include vegetables in every meal']
    };
  }

  private getDefaultWorkoutPlan(): WorkoutPlan {
    return {
      name: 'Basic Fitness Plan',
      duration: 45,
      exercises: [
        {
          name: 'Push-ups',
          sets: 3,
          reps: '10-15',
          calories: 50,
          instructions: ['Start in plank position', 'Lower body to ground', 'Push back up']
        }
      ],
      difficulty: 'beginner'
    };
  }

  private calculateBMR(profile: UserProfile): number {
    // Mifflin-St Jeor Equation
    if (profile.gender === 'male') {
      return 10 * profile.weight + 6.25 * profile.height - 5 * profile.age + 5;
    } else {
      return 10 * profile.weight + 6.25 * profile.height - 5 * profile.age - 161;
    }
  }

  private getActivityMultiplier(level: string): number {
    const multipliers = {
      sedentary: 1.2,
      lightly_active: 1.375,
      moderately_active: 1.55,
      very_active: 1.725,
      extremely_active: 1.9
    };
    return multipliers[level as keyof typeof multipliers] || 1.2;
  }
}

export const geminiService = new GeminiService();
