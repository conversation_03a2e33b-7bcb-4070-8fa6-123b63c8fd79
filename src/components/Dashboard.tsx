import React, { useEffect, useState } from 'react';
import { useUserStore } from '../store/userStore';
import { useNutritionStore } from '../store/nutritionStore';
import { useWorkoutStore } from '../store/workoutStore';
import { geminiService } from '../services/geminiService';
import { Activity, Target, TrendingUp, Calendar, Zap, Apple } from 'lucide-react';
import { format } from 'date-fns';

const Dashboard: React.FC = () => {
  const { profile, weightHistory } = useUserStore();
  const { getDailyNutrition, currentDietPlan, setDietPlan, setLoading } = useNutritionStore();
  const { workoutHistory, currentWorkoutPlan, setWorkoutPlan } = useWorkoutStore();
  const [isGeneratingPlans, setIsGeneratingPlans] = useState(false);

  const today = format(new Date(), 'yyyy-MM-dd');
  const todayNutrition = getDailyNutrition(today);
  const recentWorkouts = workoutHistory.slice(0, 5);

  useEffect(() => {
    if (profile && !currentDietPlan && !currentWorkoutPlan) {
      generatePersonalizedPlans();
    }
  }, [profile]);

  const generatePersonalizedPlans = async () => {
    if (!profile) return;

    setIsGeneratingPlans(true);
    setLoading(true);

    try {
      // Generate diet plan
      const dietPlan = await geminiService.generateDietPlan(profile, weightHistory);
      setDietPlan(dietPlan);

      // Generate workout plan
      const workoutPlan = await geminiService.generateWorkoutPlan(profile);
      setWorkoutPlan(workoutPlan);
    } catch (error) {
      console.error('Error generating plans:', error);
    } finally {
      setIsGeneratingPlans(false);
      setLoading(false);
    }
  };

  const getWeightTrend = () => {
    if (weightHistory.length < 2) return { trend: 'stable', change: 0 };

    const recent = weightHistory.slice(-2);
    const change = recent[1].weight - recent[0].weight;

    if (change > 0.5) return { trend: 'up', change };
    if (change < -0.5) return { trend: 'down', change };
    return { trend: 'stable', change };
  };

  const getCalorieProgress = () => {
    if (!currentDietPlan) return 0;
    return Math.round((todayNutrition.totalCalories / currentDietPlan.dailyCalories) * 100);
  };

  const weightTrend = getWeightTrend();
  const calorieProgress = getCalorieProgress();

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to FitAI</h2>
          <p className="text-gray-600 mb-6">Complete your profile to get started with personalized fitness recommendations</p>
          <button className="btn btn-primary">Complete Profile</button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {profile.gender === 'male' ? 'Sir' : profile.gender === 'female' ? 'Ma\'am' : 'Friend'}!
          </h1>
          <p className="text-gray-600 mt-2">Here's your fitness overview for today</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Current Weight */}
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Current Weight</p>
                <p className="text-2xl font-bold text-gray-900">{profile.weight} kg</p>
                <div className="flex items-center mt-1">
                  <TrendingUp
                    className={`h-4 w-4 mr-1 ${
                      weightTrend.trend === 'up' ? 'text-red-500' :
                      weightTrend.trend === 'down' ? 'text-green-500' : 'text-gray-500'
                    }`}
                  />
                  <span className={`text-sm ${
                    weightTrend.trend === 'up' ? 'text-red-500' :
                    weightTrend.trend === 'down' ? 'text-green-500' : 'text-gray-500'
                  }`}>
                    {weightTrend.change > 0 ? '+' : ''}{weightTrend.change.toFixed(1)} kg
                  </span>
                </div>
              </div>
              <Target className="h-8 w-8 text-primary-500" />
            </div>
          </div>

          {/* Today's Calories */}
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Calories Today</p>
                <p className="text-2xl font-bold text-gray-900">{todayNutrition.totalCalories}</p>
                <div className="flex items-center mt-1">
                  <div className="progress-bar mr-2">
                    <div
                      className="progress-fill primary"
                      style={{ width: `${Math.min(calorieProgress, 100)}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-500">{calorieProgress}%</span>
                </div>
              </div>
              <Apple className="h-8 w-8 text-green-500" />
            </div>
          </div>

          {/* Workouts This Week */}
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Workouts This Week</p>
                <p className="text-2xl font-bold text-gray-900">{recentWorkouts.length}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {recentWorkouts.reduce((sum, w) => sum + w.totalCaloriesBurned, 0)} cal burned
                </p>
              </div>
              <Activity className="h-8 w-8 text-orange-500" />
            </div>
          </div>

          {/* Body Type */}
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Body Type</p>
                <p className="text-2xl font-bold text-gray-900 capitalize">{profile.bodyType || 'Unknown'}</p>
                <p className="text-sm text-gray-500 mt-1 capitalize">{profile.goal?.replace('_', ' ')}</p>
              </div>
              <Zap className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        </div>

        {/* AI Plans Generation */}
        {isGeneratingPlans && (
          <div className="card mb-8">
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mr-4"></div>
              <p className="text-lg font-medium text-gray-900">
                AI is generating your personalized diet and workout plans...
              </p>
            </div>
          </div>
        )}

        {/* Today's Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Diet Plan Overview */}
          {currentDietPlan && (
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Nutrition Plan</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Target Calories</span>
                  <span className="font-medium">{currentDietPlan.dailyCalories}</span>
                </div>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-sm text-gray-600">Protein</p>
                    <p className="font-bold text-blue-600">{currentDietPlan.macros.protein}g</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Carbs</p>
                    <p className="font-bold text-green-600">{currentDietPlan.macros.carbs}g</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Fat</p>
                    <p className="font-bold text-yellow-600">{currentDietPlan.macros.fat}g</p>
                  </div>
                </div>
                <div className="pt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Today's Meals</h4>
                  <div className="space-y-2">
                    {currentDietPlan.meals.slice(0, 3).map((meal, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">{meal.name}</span>
                        <span className="font-medium">{meal.calories} cal</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Workout Plan Overview */}
          {currentWorkoutPlan && (
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Workout Plan</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Plan</span>
                  <span className="font-medium">{currentWorkoutPlan.name}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Duration</span>
                  <span className="font-medium">{currentWorkoutPlan.duration} min</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Difficulty</span>
                  <span className="font-medium capitalize">{currentWorkoutPlan.difficulty}</span>
                </div>
                <div className="pt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Exercises</h4>
                  <div className="space-y-2">
                    {currentWorkoutPlan.exercises.slice(0, 4).map((exercise, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">{exercise.name}</span>
                        <span className="font-medium">{exercise.sets} × {exercise.reps}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <button className="card hover:shadow-lg transition-shadow cursor-pointer text-left">
            <div className="flex items-center">
              <Apple className="h-8 w-8 text-green-500 mr-4" />
              <div>
                <h3 className="font-semibold text-gray-900">Log Food</h3>
                <p className="text-sm text-gray-600">Track your meals and calories</p>
              </div>
            </div>
          </button>

          <button className="card hover:shadow-lg transition-shadow cursor-pointer text-left">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-orange-500 mr-4" />
              <div>
                <h3 className="font-semibold text-gray-900">Start Workout</h3>
                <p className="text-sm text-gray-600">Begin your training session</p>
              </div>
            </div>
          </button>

          <button className="card hover:shadow-lg transition-shadow cursor-pointer text-left">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-purple-500 mr-4" />
              <div>
                <h3 className="font-semibold text-gray-900">Log Weight</h3>
                <p className="text-sm text-gray-600">Update your progress</p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
