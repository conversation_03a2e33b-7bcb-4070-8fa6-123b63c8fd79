import { useState, useRef, useCallback } from 'react';
import type { ChangeEvent } from 'react';
import type { UploadedImage } from '../types/index';

interface FoodScannerProps {
  onImageUploaded: (image: UploadedImage) => void;
  isAnalyzing: boolean;
}

const FoodScanner = ({ onImageUploaded, isAnalyzing }: FoodScannerProps) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    processFile(file);
  };

  const processFile = (file: File) => {
    if (!file.type.match('image.*')) {
      alert('Please upload an image file (JPEG, PNG, etc.)');
      return;
    }

    // Validate image size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Image size must be less than 10MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const dataUrl = e.target?.result as string;
      validateAndSetImage(dataUrl, file);
    };
    reader.readAsDataURL(file);
  };

  const validateAndSetImage = (dataUrl: string, file: File) => {
    // Basic image quality validation
    const img = new Image();
    img.onload = () => {
      const minWidth = 200;
      const minHeight = 200;
      
      if (img.width < minWidth || img.height < minHeight) {
        alert(`Image resolution too low. Minimum size: ${minWidth}x${minHeight}px`);
        return;
      }

      setPreviewUrl(dataUrl);
      onImageUploaded({ dataUrl, file });
      
      // Stop camera if it was active
      if (streamRef.current) {
        stopCamera();
      }
    };
    img.src = dataUrl;
  };

  const startCamera = async () => {
    try {
      setCameraError(null);
      setIsScanning(true);
      
      const constraints = {
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: { ideal: 'environment' }, // Back camera preferred
          focusMode: 'continuous'
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }
    } catch (error) {
      console.error('Camera access error:', error);
      setCameraError('Unable to access camera. Please check permissions or use file upload instead.');
      setIsScanning(false);
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsScanning(false);
    setCameraError(null);
  };

  const captureImage = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob and create file
    canvas.toBlob((blob) => {
      if (!blob) return;

      const file = new File([blob], `food-scan-${Date.now()}.jpg`, {
        type: 'image/jpeg'
      });

      const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
      validateAndSetImage(dataUrl, file);
    }, 'image/jpeg', 0.9);
  }, []);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    const file = e.dataTransfer.files?.[0];
    if (!file) return;
    
    processFile(file);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const clearImage = () => {
    setPreviewUrl(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
    if (streamRef.current) stopCamera();
  };

  return (
    <div className="w-full">
      {/* Action Buttons */}
      <div className="flex gap-3 mb-4">
        <button
          onClick={triggerFileInput}
          disabled={isAnalyzing}
          className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          📁 Upload Photo
        </button>
        
        <button
          onClick={isScanning ? stopCamera : startCamera}
          disabled={isAnalyzing}
          className={`flex-1 px-4 py-3 rounded-xl focus:outline-none focus:ring-2 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed ${
            isScanning 
              ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white' 
              : 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white'
          }`}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          📷 {isScanning ? 'Stop Camera' : 'Live Scan'}
        </button>
      </div>

      {/* Camera Error */}
      {cameraError && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-xl text-sm">
          ⚠️ {cameraError}
        </div>
      )}

      {/* Camera View */}
      {isScanning && (
        <div className="mb-4 bg-black rounded-xl overflow-hidden relative">
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className="w-full h-64 object-cover"
          />
          <div className="absolute inset-0 border-4 border-green-400 rounded-xl pointer-events-none">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 border-2 border-white rounded-lg"></div>
          </div>
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <button
              onClick={captureImage}
              disabled={isAnalyzing}
              className="w-16 h-16 bg-white rounded-full border-4 border-green-500 hover:bg-green-50 focus:outline-none focus:ring-4 focus:ring-green-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="w-8 h-8 bg-green-500 rounded-full"></div>
            </button>
          </div>
          <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg text-sm">
            🍽️ Position food in center frame
          </div>
        </div>
      )}

      {/* Upload Area */}
      {!isScanning && (
        <div
          className={`border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-colors ${
            isDragging ? 'border-green-500 bg-green-50' : 'border-gray-300 hover:border-green-400'
          } ${isAnalyzing ? 'opacity-50 cursor-not-allowed' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={!isAnalyzing ? triggerFileInput : undefined}
        >
          {previewUrl ? (
            <div className="relative">
              <img 
                src={previewUrl} 
                alt="Food to analyze" 
                className="max-h-64 mx-auto rounded-lg" 
              />
              {!isAnalyzing && (
                <button 
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    clearImage();
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
              <div className="mt-2 text-sm text-green-600 font-medium">
                ✓ Image ready for AI analysis
              </div>
            </div>
          ) : (
            <div>
              <div className="text-6xl mb-4">🍽️</div>
              <p className="text-lg font-medium text-gray-700 mb-2">
                Scan Your Food
              </p>
              <p className="text-sm text-gray-600 mb-2">
                Drag and drop an image here, or click to select
              </p>
              <p className="text-xs text-gray-500">
                PNG, JPG - Maximum 10MB
              </p>
            </div>
          )}
        </div>
      )}

      {/* Hidden elements */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};

export default FoodScanner;
