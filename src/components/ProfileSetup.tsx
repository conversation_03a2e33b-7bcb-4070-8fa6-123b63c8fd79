import React, { useState } from 'react';
import { useUserStore } from '../store/userStore';
import { geminiService } from '../services/geminiService';
import { UserProfile } from '../types';
import { User, Target, Activity, Heart } from 'lucide-react';

const ProfileSetup: React.FC = () => {
  const { setProfile } = useUserStore();
  const [currentStep, setCurrentStep] = useState(1);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [formData, setFormData] = useState<Partial<UserProfile>>({
    age: undefined,
    gender: undefined,
    height: undefined,
    weight: undefined,
    activityLevel: undefined,
    goal: undefined,
    medicalConditions: [],
    allergies: [],
    dietaryPreferences: []
  });

  const handleInputChange = (field: keyof UserProfile, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleArrayInput = (field: 'medicalConditions' | 'allergies' | 'dietaryPreferences', value: string) => {
    if (value.trim()) {
      const items = value.split(',').map(item => item.trim()).filter(Boolean);
      setFormData(prev => ({ ...prev, [field]: items }));
    }
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeProfile = async () => {
    if (!formData.age || !formData.gender || !formData.height || !formData.weight ||
        !formData.activityLevel || !formData.goal) {
      alert('Please fill in all required fields');
      return;
    }

    setIsAnalyzing(true);

    try {
      // Analyze body type using AI
      const bodyType = await geminiService.analyzeBodyType(formData);

      const completeProfile: UserProfile = {
        ...formData as UserProfile,
        bodyType: bodyType as 'ectomorph' | 'mesomorph' | 'endomorph'
      };

      setProfile(completeProfile);
    } catch (error) {
      console.error('Error analyzing profile:', error);
      // Set profile without body type analysis
      setProfile(formData as UserProfile);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <User className="h-16 w-16 text-primary-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900">Basic Information</h2>
              <p className="text-gray-600">Tell us about yourself</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Age</label>
                <input
                  type="number"
                  className="input-field"
                  placeholder="Enter your age"
                  value={formData.age || ''}
                  onChange={(e) => handleInputChange('age', parseInt(e.target.value))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                <select
                  className="input-field"
                  value={formData.gender || ''}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                >
                  <option value="">Select gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Height (cm)</label>
                <input
                  type="number"
                  className="input-field"
                  placeholder="Enter your height"
                  value={formData.height || ''}
                  onChange={(e) => handleInputChange('height', parseInt(e.target.value))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Weight (kg)</label>
                <input
                  type="number"
                  className="input-field"
                  placeholder="Enter your weight"
                  value={formData.weight || ''}
                  onChange={(e) => handleInputChange('weight', parseFloat(e.target.value))}
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Activity className="h-16 w-16 text-primary-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900">Activity Level</h2>
              <p className="text-gray-600">How active are you?</p>
            </div>

            <div className="space-y-4">
              {[
                { value: 'sedentary', label: 'Sedentary', desc: 'Little or no exercise' },
                { value: 'lightly_active', label: 'Lightly Active', desc: 'Light exercise 1-3 days/week' },
                { value: 'moderately_active', label: 'Moderately Active', desc: 'Moderate exercise 3-5 days/week' },
                { value: 'very_active', label: 'Very Active', desc: 'Hard exercise 6-7 days/week' },
                { value: 'extremely_active', label: 'Extremely Active', desc: 'Very hard exercise, physical job' }
              ].map((option) => (
                <label key={option.value} className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="activityLevel"
                    value={option.value}
                    checked={formData.activityLevel === option.value}
                    onChange={(e) => handleInputChange('activityLevel', e.target.value)}
                    className="mr-4"
                  />
                  <div>
                    <div className="font-medium text-gray-900">{option.label}</div>
                    <div className="text-sm text-gray-600">{option.desc}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Target className="h-16 w-16 text-primary-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900">Fitness Goals</h2>
              <p className="text-gray-600">What do you want to achieve?</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { value: 'lose_weight', label: 'Lose Weight', desc: 'Reduce body weight and fat' },
                { value: 'maintain_weight', label: 'Maintain Weight', desc: 'Stay at current weight' },
                { value: 'gain_weight', label: 'Gain Weight', desc: 'Increase overall body weight' },
                { value: 'build_muscle', label: 'Build Muscle', desc: 'Increase muscle mass and strength' }
              ].map((option) => (
                <label key={option.value} className="flex flex-col p-6 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="goal"
                    value={option.value}
                    checked={formData.goal === option.value}
                    onChange={(e) => handleInputChange('goal', e.target.value)}
                    className="mb-3"
                  />
                  <div className="font-medium text-gray-900 mb-1">{option.label}</div>
                  <div className="text-sm text-gray-600">{option.desc}</div>
                </label>
              ))}
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Heart className="h-16 w-16 text-primary-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900">Health & Preferences</h2>
              <p className="text-gray-600">Help us personalize your experience</p>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Medical Conditions (optional)
                </label>
                <input
                  type="text"
                  className="input-field"
                  placeholder="e.g., diabetes, hypertension (separate with commas)"
                  onChange={(e) => handleArrayInput('medicalConditions', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Food Allergies (optional)
                </label>
                <input
                  type="text"
                  className="input-field"
                  placeholder="e.g., nuts, dairy, gluten (separate with commas)"
                  onChange={(e) => handleArrayInput('allergies', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dietary Preferences (optional)
                </label>
                <input
                  type="text"
                  className="input-field"
                  placeholder="e.g., vegetarian, vegan, keto (separate with commas)"
                  onChange={(e) => handleArrayInput('dietaryPreferences', e.target.value)}
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <div className="card">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">Step {currentStep} of 4</span>
              <span className="text-sm text-gray-500">{Math.round((currentStep / 4) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(currentStep / 4) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Step Content */}
          {renderStep()}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="btn btn-secondary"
            >
              Previous
            </button>

            {currentStep < 4 ? (
              <button onClick={nextStep} className="btn btn-primary">
                Next
              </button>
            ) : (
              <button
                onClick={completeProfile}
                disabled={isAnalyzing}
                className="btn btn-primary"
              >
                {isAnalyzing ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Analyzing...
                  </div>
                ) : (
                  'Complete Profile'
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSetup;
