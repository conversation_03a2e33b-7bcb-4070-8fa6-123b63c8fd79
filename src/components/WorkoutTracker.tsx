import React, { useState } from 'react';
import { useWorkoutStore, type WorkoutSession } from '../store/workoutStore';
import { useUserStore } from '../store/userStore';
import { Play, Pause, Square, Timer, Zap, Target } from 'lucide-react';
import { format } from 'date-fns';

const WorkoutTracker: React.FC = () => {
  const { currentWorkoutPlan, workoutHistory, addWorkoutSession } = useWorkoutStore();
  const { profile } = useUserStore();
  const [isWorkoutActive, setIsWorkoutActive] = useState(false);
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [currentSet, setCurrentSet] = useState(1);
  const [workoutTimer, setWorkoutTimer] = useState(0);
  const [restTimer, setRestTimer] = useState(0);
  const [isResting, setIsResting] = useState(false);

  React.useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isWorkoutActive && !isResting) {
      interval = setInterval(() => {
        setWorkoutTimer(prev => prev + 1);
      }, 1000);
    } else if (isResting && restTimer > 0) {
      interval = setInterval(() => {
        setRestTimer(prev => {
          if (prev <= 1) {
            setIsResting(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isWorkoutActive, isResting, restTimer]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startWorkout = () => {
    setIsWorkoutActive(true);
    setCurrentExerciseIndex(0);
    setCurrentSet(1);
    setWorkoutTimer(0);
  };

  const pauseWorkout = () => {
    setIsWorkoutActive(false);
  };

  const endWorkout = () => {
    if (workoutTimer > 0) {
      const session: Omit<WorkoutSession, 'id'> = {
        date: format(new Date(), 'yyyy-MM-dd'),
        workoutPlanId: currentWorkoutPlan?.id || 'custom',
        exercises: currentWorkoutPlan?.exercises.slice(0, currentExerciseIndex + 1) || [],
        duration: workoutTimer,
        totalCaloriesBurned: Math.round(workoutTimer * 0.1), // Rough estimate
        notes: `Completed ${currentExerciseIndex + 1} exercises`
      };
      addWorkoutSession(session);
    }
    
    setIsWorkoutActive(false);
    setCurrentExerciseIndex(0);
    setCurrentSet(1);
    setWorkoutTimer(0);
    setIsResting(false);
    setRestTimer(0);
  };

  const nextExercise = () => {
    if (currentWorkoutPlan && currentExerciseIndex < currentWorkoutPlan.exercises.length - 1) {
      setCurrentExerciseIndex(prev => prev + 1);
      setCurrentSet(1);
      setIsResting(true);
      setRestTimer(60); // 1 minute rest
    } else {
      endWorkout();
    }
  };

  const nextSet = () => {
    const currentExercise = currentWorkoutPlan?.exercises[currentExerciseIndex];
    if (currentExercise && currentSet < currentExercise.sets) {
      setCurrentSet(prev => prev + 1);
      setIsResting(true);
      setRestTimer(30); // 30 seconds rest between sets
    } else {
      nextExercise();
    }
  };

  if (!currentWorkoutPlan) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto px-4 text-center">
          <div className="text-6xl mb-4">💪</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">No Workout Plan</h2>
          <p className="text-gray-600 mb-6">Complete your profile to get a personalized workout plan</p>
          <button className="btn btn-primary">Go to Dashboard</button>
        </div>
      </div>
    );
  }

  const currentExercise = currentWorkoutPlan.exercises[currentExerciseIndex];
  const progress = ((currentExerciseIndex + 1) / currentWorkoutPlan.exercises.length) * 100;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-md mx-auto px-4 py-6">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">{currentWorkoutPlan.name}</h1>
          <p className="text-gray-600">{currentWorkoutPlan.difficulty} • {currentWorkoutPlan.duration} min</p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">
              Exercise {currentExerciseIndex + 1} of {currentWorkoutPlan.exercises.length}
            </span>
            <span className="text-sm text-gray-500">{Math.round(progress)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Timer */}
        <div className="bg-white rounded-xl p-6 shadow-sm border mb-6">
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 mb-2">
              {formatTime(workoutTimer)}
            </div>
            <div className="text-sm text-gray-600">Workout Time</div>
            {isResting && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-600">Rest: {formatTime(restTimer)}</div>
                <div className="text-sm text-blue-600">Take a break!</div>
              </div>
            )}
          </div>
        </div>

        {/* Current Exercise */}
        <div className="bg-white rounded-xl p-6 shadow-sm border mb-6">
          <div className="text-center mb-4">
            <h3 className="text-xl font-bold text-gray-900 mb-2">{currentExercise.name}</h3>
            <div className="flex justify-center items-center space-x-4 text-sm text-gray-600">
              <span>Set {currentSet} of {currentExercise.sets}</span>
              <span>•</span>
              <span>{currentExercise.reps} reps</span>
              {currentExercise.weight && (
                <>
                  <span>•</span>
                  <span>{currentExercise.weight} kg</span>
                </>
              )}
            </div>
          </div>

          {currentExercise.instructions && (
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <p className="text-sm text-gray-700">{currentExercise.instructions}</p>
            </div>
          )}

          {/* Exercise Controls */}
          <div className="flex justify-center space-x-3">
            {!isWorkoutActive ? (
              <button
                onClick={startWorkout}
                className="btn btn-primary flex items-center"
              >
                <Play className="h-4 w-4 mr-2" />
                Start Workout
              </button>
            ) : (
              <>
                <button
                  onClick={pauseWorkout}
                  className="btn btn-secondary flex items-center"
                >
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </button>
                <button
                  onClick={nextSet}
                  disabled={isResting}
                  className="btn btn-primary flex items-center"
                >
                  <Target className="h-4 w-4 mr-2" />
                  Complete Set
                </button>
                <button
                  onClick={endWorkout}
                  className="btn btn-secondary flex items-center"
                >
                  <Square className="h-4 w-4 mr-2" />
                  End
                </button>
              </>
            )}
          </div>
        </div>

        {/* Exercise List */}
        <div className="bg-white rounded-xl p-6 shadow-sm border">
          <h4 className="font-semibold text-gray-900 mb-4">Exercise List</h4>
          <div className="space-y-3">
            {currentWorkoutPlan.exercises.map((exercise, index) => (
              <div
                key={index}
                className={`flex justify-between items-center p-3 rounded-lg ${
                  index === currentExerciseIndex
                    ? 'bg-orange-50 border border-orange-200'
                    : index < currentExerciseIndex
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-gray-50'
                }`}
              >
                <div>
                  <div className="font-medium text-gray-900">{exercise.name}</div>
                  <div className="text-sm text-gray-600">
                    {exercise.sets} × {exercise.reps}
                    {exercise.weight && ` @ ${exercise.weight}kg`}
                  </div>
                </div>
                <div className="text-right">
                  {index < currentExerciseIndex ? (
                    <div className="text-green-600 font-medium">✓ Done</div>
                  ) : index === currentExerciseIndex ? (
                    <div className="text-orange-600 font-medium">Current</div>
                  ) : (
                    <div className="text-gray-400">Pending</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkoutTracker;
