import React, { useState } from 'react';
import { useNutritionStore, FoodEntry } from '../store/nutritionStore';
import { geminiService } from '../services/geminiService';
import { format } from 'date-fns';

const CalorieTracker: React.FC = () => {
  const {
    getDailyNutrition,
    addFoodEntry,
    removeFoodEntry,
    updateFoodEntry,
    currentDietPlan
  } = useNutritionStore();

  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const dailyNutrition = getDailyNutrition(selectedDate);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsAnalyzing(true);

    try {
      // Convert image to base64
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64 = e.target?.result as string;
        const base64Data = base64.split(',')[1]; // Remove data:image/jpeg;base64, prefix

        try {
          const analysis = await geminiService.analyzeFoodImage(base64Data);

          // Show Cal AI style confirmation dialog
          const confirmed = confirm(`
🤖 AI detected: ${analysis.food}
📊 Estimated: ${analysis.calories} calories

Macros:
• Protein: ${analysis.macros.protein}g
• Carbs: ${analysis.macros.carbs}g
• Fat: ${analysis.macros.fat}g

Click OK to add, or Cancel to adjust manually.
          `);

          if (confirmed) {
            // Auto-detect meal type based on time
            const hour = new Date().getHours();
            let mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack' = 'snack';
            if (hour >= 6 && hour < 11) mealType = 'breakfast';
            else if (hour >= 11 && hour < 16) mealType = 'lunch';
            else if (hour >= 16 && hour < 22) mealType = 'dinner';

            const foodEntry: Omit<FoodEntry, 'id'> = {
              date: selectedDate,
              name: analysis.food,
              calories: analysis.calories,
              protein: analysis.macros.protein,
              carbs: analysis.macros.carbs,
              fat: analysis.macros.fat,
              meal: mealType,
              imageUrl: base64
            };

            addFoodEntry(foodEntry);
          }
        } catch (error) {
          console.error('Error analyzing food image:', error);
          alert('🤖 AI had trouble analyzing this image. Try a clearer photo or add manually.');
        } finally {
          setIsAnalyzing(false);
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error processing image:', error);
      setIsAnalyzing(false);
    }
  };




  const foodsByMeal = {
    breakfast: dailyNutrition.foods.filter(f => f.meal === 'breakfast'),
    lunch: dailyNutrition.foods.filter(f => f.meal === 'lunch'),
    dinner: dailyNutrition.foods.filter(f => f.meal === 'dinner'),
    snack: dailyNutrition.foods.filter(f => f.meal === 'snack')
  };

  return (
    <div className="min-h-screen bg-gray-50" style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      {/* Header - Cal AI Style */}
      <div className="flex justify-between items-center mb-6">
        <div style={{ width: '24px' }}></div>
        <h1 className="text-xl font-bold text-gray-900">Today</h1>
        <input
          type="date"
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          className="text-sm border-none bg-transparent"
        />
      </div>

      {/* Calorie Ring - Cal AI Style */}
      <div className="text-center mb-8">
        <div className="relative inline-block">
          <svg width="200" height="200" style={{ transform: 'rotate(-90deg)' }}>
            <circle
              cx="100"
              cy="100"
              r="80"
              fill="none"
              stroke="#e5e7eb"
              strokeWidth="12"
            />
            <circle
              cx="100"
              cy="100"
              r="80"
              fill="none"
              stroke="#10b981"
              strokeWidth="12"
              strokeDasharray={`${2 * Math.PI * 80}`}
              strokeDashoffset={`${2 * Math.PI * 80 * (1 - Math.min(dailyNutrition.totalCalories / (currentDietPlan?.dailyCalories || 2000), 1))}`}
              strokeLinecap="round"
            />
          </svg>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
            <div className="text-3xl font-bold text-gray-900">{dailyNutrition.totalCalories}</div>
            <div className="text-sm text-gray-600">of {currentDietPlan?.dailyCalories || 2000}</div>
            <div className="text-xs text-gray-600">calories</div>
          </div>
        </div>
      </div>

      {/* Macro Breakdown - Cal AI Style */}
      <div className="grid grid-cols-3 gap-3 mb-8">
        <div className="bg-white p-4 rounded-lg text-center">
          <div className="text-lg font-bold text-blue-600">{dailyNutrition.totalProtein.toFixed(0)}g</div>
          <div className="text-xs text-gray-600 mb-2">Protein</div>
          <div className="bg-gray-200 h-1 rounded">
            <div
              className="bg-blue-600 h-1 rounded"
              style={{ width: `${Math.min((dailyNutrition.totalProtein / (currentDietPlan?.macros.protein || 150)) * 100, 100)}%` }}
            ></div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg text-center">
          <div className="text-lg font-bold text-yellow-600">{dailyNutrition.totalCarbs.toFixed(0)}g</div>
          <div className="text-xs text-gray-600 mb-2">Carbs</div>
          <div className="bg-gray-200 h-1 rounded">
            <div
              className="bg-yellow-600 h-1 rounded"
              style={{ width: `${Math.min((dailyNutrition.totalCarbs / (currentDietPlan?.macros.carbs || 200)) * 100, 100)}%` }}
            ></div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg text-center">
          <div className="text-lg font-bold text-red-500">{dailyNutrition.totalFat.toFixed(0)}g</div>
          <div className="text-xs text-gray-600 mb-2">Fat</div>
          <div className="bg-gray-200 h-1 rounded">
            <div
              className="bg-red-500 h-1 rounded"
              style={{ width: `${Math.min((dailyNutrition.totalFat / (currentDietPlan?.macros.fat || 65)) * 100, 100)}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Add Food Button - Cal AI Style */}
      <div className="mb-8">
        <label className="block bg-green-500 text-white p-4 rounded-lg text-center cursor-pointer font-bold">
          📸 Snap a photo to add food
          <input
            type="file"
            accept="image/*"
            capture="environment"
            className="hidden"
            onChange={handleImageUpload}
          />
        </label>
        {isAnalyzing && (
          <div className="text-center mt-3 text-gray-600">
            <div className="text-xl mb-1">🤖</div>
            <div>AI analyzing your food...</div>
          </div>
        )}
      </div>
      {/* Meals by Time - Cal AI Style */}
      {Object.entries(foodsByMeal).map(([mealType, foods]) => {
        const mealCalories = foods.reduce((sum, food) => sum + food.calories, 0);
        const mealEmojis = { breakfast: '🌅', lunch: '☀️', dinner: '🌙', snack: '🍎' };

        return (
          <div key={mealType} className="mb-5">
            <div className={`bg-white rounded-lg p-4 ${foods.length > 0 ? 'border-2 border-green-500' : 'border border-gray-200'}`}>
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center gap-2">
                  <span className="text-xl">{mealEmojis[mealType as keyof typeof mealEmojis]}</span>
                  <span className="font-bold capitalize">{mealType}</span>
                </div>
                <span className="text-sm font-bold text-gray-600">{mealCalories} cal</span>
              </div>

              {foods.length === 0 ? (
                <button
                  onClick={() => {
                    const foodName = prompt(`Add food to ${mealType}:`);
                    if (foodName) {
                      const calories = parseInt(prompt('Estimated calories:') || '100');
                      const foodEntry: Omit<FoodEntry, 'id'> = {
                        date: selectedDate,
                        name: foodName,
                        calories,
                        protein: Math.round(calories * 0.15 / 4),
                        carbs: Math.round(calories * 0.5 / 4),
                        fat: Math.round(calories * 0.35 / 9),
                        meal: mealType as 'breakfast' | 'lunch' | 'dinner' | 'snack'
                      };
                      addFoodEntry(foodEntry);
                    }
                  }}
                  className="w-full p-3 bg-gray-100 border border-dashed border-gray-300 rounded-lg text-gray-600 cursor-pointer hover:bg-gray-200"
                >
                  + Add {mealType}
                </button>
              ) : (
                <div className="space-y-2">
                  {foods.map(food => (
                    <div key={food.id} className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        {food.imageUrl && (
                          <img
                            src={food.imageUrl}
                            alt={food.name}
                            className="w-10 h-10 rounded-lg object-cover"
                          />
                        )}
                        <div>
                          <div className="font-bold text-sm">{food.name}</div>
                          <div className="text-xs text-gray-600">
                            P: {food.protein}g • C: {food.carbs}g • F: {food.fat}g
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-bold">{food.calories}</span>
                        <button
                          onClick={() => removeFoodEntry(food.id, selectedDate)}
                          className="bg-red-500 text-white rounded px-2 py-1 text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        );
      })}

      {/* Quick Add Suggestions - Cal AI Style */}
      <div className="mt-6">
        <h3 className="font-semibold mb-3">Quick Add</h3>
        <div className="grid grid-cols-2 gap-2">
          {[
            { name: 'Water (8oz)', calories: 0 },
            { name: 'Coffee (black)', calories: 5 },
            { name: 'Apple (medium)', calories: 95 },
            { name: 'Banana (medium)', calories: 105 }
          ].map(item => (
            <button
              key={item.name}
              onClick={() => {
                const foodEntry: Omit<FoodEntry, 'id'> = {
                  date: selectedDate,
                  name: item.name,
                  calories: item.calories,
                  protein: 0,
                  carbs: item.calories / 4,
                  fat: 0,
                  meal: 'snack'
                };
                addFoodEntry(foodEntry);
              }}
              className="p-2 bg-white border border-gray-200 rounded-lg text-xs cursor-pointer hover:bg-gray-50"
            >
              {item.name}
            </button>
          ))}
        </div>
      </div>

    </div>
  );
};

export default CalorieTracker;
