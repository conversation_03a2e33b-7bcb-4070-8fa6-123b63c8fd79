import React, { useState, useRef } from 'react';
import { useNutritionStore, FoodEntry } from '../store/nutritionStore';
import { geminiService } from '../services/geminiService';
import { Camera, Plus, Search, Trash2, Edit } from 'lucide-react';
import { format } from 'date-fns';

const CalorieTracker: React.FC = () => {
  const {
    getDailyNutrition,
    addFoodEntry,
    removeFoodEntry,
    updateFoodEntry,
    currentDietPlan
  } = useNutritionStore();

  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [isAddingFood, setIsAddingFood] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [manualEntry, setManualEntry] = useState({
    name: '',
    calories: '',
    protein: '',
    carbs: '',
    fat: '',
    meal: 'breakfast' as 'breakfast' | 'lunch' | 'dinner' | 'snack'
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dailyNutrition = getDailyNutrition(selectedDate);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsAnalyzing(true);

    try {
      // Convert image to base64
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64 = e.target?.result as string;
        const base64Data = base64.split(',')[1]; // Remove data:image/jpeg;base64, prefix

        try {
          const analysis = await geminiService.analyzeFoodImage(base64Data);

          const foodEntry: Omit<FoodEntry, 'id'> = {
            date: selectedDate,
            name: analysis.food,
            calories: analysis.calories,
            protein: analysis.macros.protein,
            carbs: analysis.macros.carbs,
            fat: analysis.macros.fat,
            meal: manualEntry.meal,
            imageUrl: base64
          };

          addFoodEntry(foodEntry);
          setIsAddingFood(false);
        } catch (error) {
          console.error('Error analyzing food image:', error);
          alert('Failed to analyze food image. Please try manual entry.');
        } finally {
          setIsAnalyzing(false);
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error processing image:', error);
      setIsAnalyzing(false);
    }
  };

  const handleManualAdd = () => {
    if (!manualEntry.name || !manualEntry.calories) {
      alert('Please enter at least food name and calories');
      return;
    }

    const foodEntry: Omit<FoodEntry, 'id'> = {
      date: selectedDate,
      name: manualEntry.name,
      calories: parseInt(manualEntry.calories),
      protein: parseFloat(manualEntry.protein) || 0,
      carbs: parseFloat(manualEntry.carbs) || 0,
      fat: parseFloat(manualEntry.fat) || 0,
      meal: manualEntry.meal
    };

    addFoodEntry(foodEntry);
    setManualEntry({
      name: '',
      calories: '',
      protein: '',
      carbs: '',
      fat: '',
      meal: 'breakfast'
    });
    setIsAddingFood(false);
  };

  const getCalorieProgress = () => {
    if (!currentDietPlan) return 0;
    return Math.round((dailyNutrition.totalCalories / currentDietPlan.dailyCalories) * 100);
  };

  const getMacroProgress = (macro: 'protein' | 'carbs' | 'fat') => {
    if (!currentDietPlan) return 0;
    const current = macro === 'protein' ? dailyNutrition.totalProtein :
                   macro === 'carbs' ? dailyNutrition.totalCarbs :
                   dailyNutrition.totalFat;
    const target = currentDietPlan.macros[macro];
    return Math.round((current / target) * 100);
  };

  const filteredFoods = dailyNutrition.foods.filter(food =>
    food.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const foodsByMeal = {
    breakfast: filteredFoods.filter(f => f.meal === 'breakfast'),
    lunch: filteredFoods.filter(f => f.meal === 'lunch'),
    dinner: filteredFoods.filter(f => f.meal === 'dinner'),
    snack: filteredFoods.filter(f => f.meal === 'snack')
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Calorie Tracker</h1>
          <div className="flex items-center space-x-4">
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="input-field"
            />
            <button
              onClick={() => setIsAddingFood(true)}
              className="btn btn-primary flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Food
            </button>
          </div>
        </div>

        {/* Daily Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Calories</h3>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-gray-900">{dailyNutrition.totalCalories}</span>
              {currentDietPlan && (
                <span className="text-sm text-gray-600">/ {currentDietPlan.dailyCalories}</span>
              )}
            </div>
            {currentDietPlan && (
              <div className="progress-bar mt-2">
                <div
                  className="progress-fill primary"
                  style={{ width: `${Math.min(getCalorieProgress(), 100)}%` }}
                ></div>
              </div>
            )}
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Protein</h3>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-blue-600">{dailyNutrition.totalProtein.toFixed(1)}g</span>
              {currentDietPlan && (
                <span className="text-sm text-gray-600">/ {currentDietPlan.macros.protein}g</span>
              )}
            </div>
            {currentDietPlan && (
              <div className="progress-bar mt-2">
                <div
                  className="progress-fill blue"
                  style={{ width: `${Math.min(getMacroProgress('protein'), 100)}%` }}
                ></div>
              </div>
            )}
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Carbs</h3>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-green-600">{dailyNutrition.totalCarbs.toFixed(1)}g</span>
              {currentDietPlan && (
                <span className="text-sm text-gray-600">/ {currentDietPlan.macros.carbs}g</span>
              )}
            </div>
            {currentDietPlan && (
              <div className="progress-bar mt-2">
                <div
                  className="progress-fill green"
                  style={{ width: `${Math.min(getMacroProgress('carbs'), 100)}%` }}
                ></div>
              </div>
            )}
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Fat</h3>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-yellow-600">{dailyNutrition.totalFat.toFixed(1)}g</span>
              {currentDietPlan && (
                <span className="text-sm text-gray-600">/ {currentDietPlan.macros.fat}g</span>
              )}
            </div>
            {currentDietPlan && (
              <div className="progress-bar mt-2">
                <div
                  className="progress-fill yellow"
                  style={{ width: `${Math.min(getMacroProgress('fat'), 100)}%` }}
                ></div>
              </div>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search foods..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input-field pl-10"
            />
          </div>
        </div>

        {/* Food Entries by Meal */}
        <div className="space-y-6">
          {Object.entries(foodsByMeal).map(([mealType, foods]) => (
            <div key={mealType} className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 capitalize">{mealType}</h3>
              {foods.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No foods logged for {mealType}</p>
              ) : (
                <div className="space-y-3">
                  {foods.map((food) => (
                    <div key={food.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {food.imageUrl && (
                          <img
                            src={food.imageUrl}
                            alt={food.name}
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                        )}
                        <div>
                          <h4 className="font-medium text-gray-900">{food.name}</h4>
                          <p className="text-sm text-gray-600">
                            {food.calories} cal • P: {food.protein}g • C: {food.carbs}g • F: {food.fat}g
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => removeFoodEntry(food.id, selectedDate)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Add Food Modal */}
        {isAddingFood && (
          <div className="modal-overlay">
            <div className="modal-content">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Food</h3>

              {isAnalyzing ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
                  <p className="text-gray-600">Analyzing food image...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Camera Upload */}
                  <div className="text-center">
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="btn btn-secondary flex items-center mx-auto"
                    >
                      <Camera className="h-4 w-4 mr-2" />
                      Take Photo / Upload Image
                    </button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                  </div>

                  <div className="text-center text-gray-500">or</div>

                  {/* Manual Entry */}
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Meal</label>
                      <select
                        value={manualEntry.meal}
                        onChange={(e) => setManualEntry(prev => ({ ...prev, meal: e.target.value as any }))}
                        className="input-field"
                      >
                        <option value="breakfast">Breakfast</option>
                        <option value="lunch">Lunch</option>
                        <option value="dinner">Dinner</option>
                        <option value="snack">Snack</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Food Name</label>
                      <input
                        type="text"
                        value={manualEntry.name}
                        onChange={(e) => setManualEntry(prev => ({ ...prev, name: e.target.value }))}
                        className="input-field"
                        placeholder="e.g., Grilled Chicken Breast"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Calories</label>
                        <input
                          type="number"
                          value={manualEntry.calories}
                          onChange={(e) => setManualEntry(prev => ({ ...prev, calories: e.target.value }))}
                          className="input-field"
                          placeholder="200"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Protein (g)</label>
                        <input
                          type="number"
                          step="0.1"
                          value={manualEntry.protein}
                          onChange={(e) => setManualEntry(prev => ({ ...prev, protein: e.target.value }))}
                          className="input-field"
                          placeholder="25"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Carbs (g)</label>
                        <input
                          type="number"
                          step="0.1"
                          value={manualEntry.carbs}
                          onChange={(e) => setManualEntry(prev => ({ ...prev, carbs: e.target.value }))}
                          className="input-field"
                          placeholder="10"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Fat (g)</label>
                        <input
                          type="number"
                          step="0.1"
                          value={manualEntry.fat}
                          onChange={(e) => setManualEntry(prev => ({ ...prev, fat: e.target.value }))}
                          className="input-field"
                          placeholder="5"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      onClick={() => setIsAddingFood(false)}
                      className="btn btn-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleManualAdd}
                      className="btn btn-primary"
                    >
                      Add Food
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CalorieTracker;
