import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { WorkoutPlan, Exercise } from '../types';

export interface WorkoutSession {
  id: string;
  date: string;
  planName: string;
  exercises: CompletedExercise[];
  duration: number; // actual duration in minutes
  totalCaloriesBurned: number;
  notes?: string;
}

export interface CompletedExercise {
  name: string;
  sets: number;
  reps: string;
  weight?: number; // for strength exercises
  duration?: number; // for cardio
  caloriesBurned: number;
  completed: boolean;
}

interface WorkoutState {
  currentWorkoutPlan: WorkoutPlan | null;
  workoutHistory: WorkoutSession[];
  activeSession: WorkoutSession | null;
  isLoading: boolean;

  // Actions
  setWorkoutPlan: (plan: WorkoutPlan) => void;
  startWorkoutSession: (planName: string, exercises: Exercise[]) => void;
  completeExercise: (exerciseIndex: number, data: Partial<CompletedExercise>) => void;
  finishWorkoutSession: (notes?: string) => void;
  cancelWorkoutSession: () => void;
  addWorkoutSession: (session: Omit<WorkoutSession, 'id'>) => void;
  removeWorkoutSession: (id: string) => void;
  getWorkoutHistory: (days?: number) => WorkoutSession[];
  setLoading: (loading: boolean) => void;
  clearData: () => void;
}

export const useWorkoutStore = create<WorkoutState>()(
  persist(
    (set, get) => ({
      currentWorkoutPlan: null,
      workoutHistory: [],
      activeSession: null,
      isLoading: false,

      setWorkoutPlan: (plan) => {
        set({ currentWorkoutPlan: plan });
      },

      startWorkoutSession: (planName, exercises) => {
        const session: WorkoutSession = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          date: new Date().toISOString().split('T')[0],
          planName,
          exercises: exercises.map(exercise => ({
            name: exercise.name,
            sets: exercise.sets,
            reps: exercise.reps,
            duration: exercise.duration,
            caloriesBurned: exercise.calories,
            completed: false
          })),
          duration: 0,
          totalCaloriesBurned: 0
        };

        set({ activeSession: session });
      },

      completeExercise: (exerciseIndex, data) => {
        const { activeSession } = get();
        if (!activeSession) return;

        const updatedExercises = [...activeSession.exercises];
        updatedExercises[exerciseIndex] = {
          ...updatedExercises[exerciseIndex],
          ...data,
          completed: true
        };

        const totalCaloriesBurned = updatedExercises
          .filter(ex => ex.completed)
          .reduce((sum, ex) => sum + ex.caloriesBurned, 0);

        set({
          activeSession: {
            ...activeSession,
            exercises: updatedExercises,
            totalCaloriesBurned
          }
        });
      },

      finishWorkoutSession: (notes) => {
        const { activeSession, workoutHistory } = get();
        if (!activeSession) return;

        const completedSession: WorkoutSession = {
          ...activeSession,
          duration: Math.round((Date.now() - new Date(activeSession.date + 'T00:00:00').getTime()) / (1000 * 60)),
          notes
        };

        const updatedHistory = [completedSession, ...workoutHistory]
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

        set({
          workoutHistory: updatedHistory,
          activeSession: null
        });
      },

      cancelWorkoutSession: () => {
        set({ activeSession: null });
      },

      addWorkoutSession: (session) => {
        const { workoutHistory } = get();
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        const newSession: WorkoutSession = { ...session, id };

        const updatedHistory = [newSession, ...workoutHistory]
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

        set({ workoutHistory: updatedHistory });
      },

      removeWorkoutSession: (id) => {
        const { workoutHistory } = get();
        set({
          workoutHistory: workoutHistory.filter(session => session.id !== id)
        });
      },

      getWorkoutHistory: (days = 30) => {
        const { workoutHistory } = get();
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        return workoutHistory.filter(session =>
          new Date(session.date) >= cutoffDate
        );
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      clearData: () => {
        set({
          currentWorkoutPlan: null,
          workoutHistory: [],
          activeSession: null,
          isLoading: false
        });
      }
    }),
    {
      name: 'workout-storage',
      partialize: (state) => ({
        currentWorkoutPlan: state.currentWorkoutPlan,
        workoutHistory: state.workoutHistory
      })
    }
  )
);
