import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { DietPlan } from '../types';

export interface FoodEntry {
  id: string;
  date: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  meal: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  imageUrl?: string;
}

export interface DailyNutrition {
  date: string;
  totalCalories: number;
  totalProtein: number;
  totalCarbs: number;
  totalFat: number;
  foods: FoodEntry[];
}

interface NutritionState {
  currentDietPlan: DietPlan | null;
  dailyNutrition: Record<string, DailyNutrition>;
  isLoading: boolean;

  // Actions
  setDietPlan: (plan: DietPlan) => void;
  addFoodEntry: (entry: Omit<FoodEntry, 'id'>) => void;
  removeFoodEntry: (id: string, date: string) => void;
  updateFoodEntry: (id: string, date: string, updates: Partial<FoodEntry>) => void;
  getDailyNutrition: (date: string) => DailyNutrition;
  setLoading: (loading: boolean) => void;
  clearData: () => void;
}

const createEmptyDailyNutrition = (date: string): DailyNutrition => ({
  date,
  totalCalories: 0,
  totalProtein: 0,
  totalCarbs: 0,
  totalFat: 0,
  foods: []
});

export const useNutritionStore = create<NutritionState>()(
  persist(
    (set, get) => ({
      currentDietPlan: null,
      dailyNutrition: {},
      isLoading: false,

      setDietPlan: (plan) => {
        set({ currentDietPlan: plan });
      },

      addFoodEntry: (entry) => {
        const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
        const foodEntry: FoodEntry = { ...entry, id };

        const { dailyNutrition } = get();
        const dateKey = entry.date;

        const currentDay = dailyNutrition[dateKey] || createEmptyDailyNutrition(dateKey);
        const updatedFoods = [...currentDay.foods, foodEntry];

        const updatedDay: DailyNutrition = {
          ...currentDay,
          foods: updatedFoods,
          totalCalories: updatedFoods.reduce((sum, food) => sum + food.calories, 0),
          totalProtein: updatedFoods.reduce((sum, food) => sum + food.protein, 0),
          totalCarbs: updatedFoods.reduce((sum, food) => sum + food.carbs, 0),
          totalFat: updatedFoods.reduce((sum, food) => sum + food.fat, 0),
        };

        set({
          dailyNutrition: {
            ...dailyNutrition,
            [dateKey]: updatedDay
          }
        });
      },

      removeFoodEntry: (id, date) => {
        const { dailyNutrition } = get();
        const currentDay = dailyNutrition[date];

        if (!currentDay) return;

        const updatedFoods = currentDay.foods.filter(food => food.id !== id);

        const updatedDay: DailyNutrition = {
          ...currentDay,
          foods: updatedFoods,
          totalCalories: updatedFoods.reduce((sum, food) => sum + food.calories, 0),
          totalProtein: updatedFoods.reduce((sum, food) => sum + food.protein, 0),
          totalCarbs: updatedFoods.reduce((sum, food) => sum + food.carbs, 0),
          totalFat: updatedFoods.reduce((sum, food) => sum + food.fat, 0),
        };

        set({
          dailyNutrition: {
            ...dailyNutrition,
            [date]: updatedDay
          }
        });
      },

      updateFoodEntry: (id, date, updates) => {
        const { dailyNutrition } = get();
        const currentDay = dailyNutrition[date];

        if (!currentDay) return;

        const updatedFoods = currentDay.foods.map(food =>
          food.id === id ? { ...food, ...updates } : food
        );

        const updatedDay: DailyNutrition = {
          ...currentDay,
          foods: updatedFoods,
          totalCalories: updatedFoods.reduce((sum, food) => sum + food.calories, 0),
          totalProtein: updatedFoods.reduce((sum, food) => sum + food.protein, 0),
          totalCarbs: updatedFoods.reduce((sum, food) => sum + food.carbs, 0),
          totalFat: updatedFoods.reduce((sum, food) => sum + food.fat, 0),
        };

        set({
          dailyNutrition: {
            ...dailyNutrition,
            [date]: updatedDay
          }
        });
      },

      getDailyNutrition: (date) => {
        const { dailyNutrition } = get();
        return dailyNutrition[date] || createEmptyDailyNutrition(date);
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      clearData: () => {
        set({
          currentDietPlan: null,
          dailyNutrition: {},
          isLoading: false
        });
      }
    }),
    {
      name: 'nutrition-storage',
      partialize: (state) => ({
        currentDietPlan: state.currentDietPlan,
        dailyNutrition: state.dailyNutrition
      })
    }
  )
);
