import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface UserProfile {
  age: number;
  gender: 'male' | 'female' | 'other';
  height: number; // in cm
  weight: number; // in kg
  activityLevel: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | 'extremely_active';
  goal: 'lose_weight' | 'maintain_weight' | 'gain_weight' | 'build_muscle';
  bodyType?: 'ectomorph' | 'mesomorph' | 'endomorph';
  medicalConditions?: string[];
  allergies?: string[];
  dietaryPreferences?: string[];
}

export interface WeightEntry {
  date: string;
  weight: number;
  notes?: string;
}

interface UserState {
  profile: UserProfile | null;
  weightHistory: WeightEntry[];
  isProfileComplete: boolean;

  // Actions
  setProfile: (profile: UserProfile) => void;
  updateProfile: (updates: Partial<UserProfile>) => void;
  addWeightEntry: (entry: WeightEntry) => void;
  removeWeightEntry: (date: string) => void;
  clearData: () => void;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      profile: null,
      weightHistory: [],
      isProfileComplete: false,

      setProfile: (profile) => {
        set({
          profile,
          isProfileComplete: true
        });
      },

      updateProfile: (updates) => {
        const currentProfile = get().profile;
        if (currentProfile) {
          const updatedProfile = { ...currentProfile, ...updates };
          set({
            profile: updatedProfile,
            isProfileComplete: true
          });
        }
      },

      addWeightEntry: (entry) => {
        const { weightHistory } = get();
        const existingIndex = weightHistory.findIndex(w => w.date === entry.date);

        if (existingIndex >= 0) {
          // Update existing entry
          const updated = [...weightHistory];
          updated[existingIndex] = entry;
          set({ weightHistory: updated.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) });
        } else {
          // Add new entry
          const updated = [...weightHistory, entry].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
          set({ weightHistory: updated });
        }

        // Update profile weight with latest entry
        const { profile } = get();
        if (profile) {
          set({ profile: { ...profile, weight: entry.weight } });
        }
      },

      removeWeightEntry: (date) => {
        const { weightHistory } = get();
        set({
          weightHistory: weightHistory.filter(entry => entry.date !== date)
        });
      },

      clearData: () => {
        set({
          profile: null,
          weightHistory: [],
          isProfileComplete: false
        });
      }
    }),
    {
      name: 'user-storage',
      partialize: (state) => ({
        profile: state.profile,
        weightHistory: state.weightHistory,
        isProfileComplete: state.isProfileComplete
      })
    }
  )
);
