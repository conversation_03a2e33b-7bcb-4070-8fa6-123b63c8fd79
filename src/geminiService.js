// Simple Gemini AI service for fitness recommendations
const API_KEY = 'AIzaSyCQz5-VAVuj03vXnaOx8YLsLTWb_mptUvQ';

class GeminiService {
  async analyzeBodyType(profile) {
    const prompt = `
    Based on the following user information, determine their body type (ectomorph, mesomorph, or endomorph):
    - Age: ${profile.age}
    - Gender: ${profile.gender}
    - Height: ${profile.height}cm
    - Weight: ${profile.weight}kg
    - Activity Level: ${profile.activityLevel}
    
    Return only one word: ectomorph, mesomorph, or endomorph
    `;

    try {
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }]
        })
      });

      const data = await response.json();
      const text = data.candidates[0].content.parts[0].text.toLowerCase();
      
      if (text.includes('ectomorph')) return 'ectomorph';
      if (text.includes('mesomorph')) return 'mesomorph';
      if (text.includes('endomorph')) return 'endomorph';
      
      return 'mesomorph'; // default
    } catch (error) {
      console.error('Error analyzing body type:', error);
      return 'mesomorph';
    }
  }

  async generateDietPlan(profile, weightHistory = []) {
    const recentWeightTrend = this.analyzeWeightTrend(weightHistory);
    
    const prompt = `
    Create a personalized diet plan for a user with the following profile:
    - Age: ${profile.age}
    - Gender: ${profile.gender}
    - Height: ${profile.height}cm
    - Current Weight: ${profile.weight}kg
    - Body Type: ${profile.bodyType}
    - Activity Level: ${profile.activityLevel}
    - Goal: ${profile.goal}
    - Weight Trend: ${recentWeightTrend}

    Return a JSON object with the following structure:
    {
      "dailyCalories": number,
      "macros": {
        "protein": number (in grams),
        "carbs": number (in grams),
        "fat": number (in grams)
      },
      "meals": [
        {
          "name": string,
          "calories": number,
          "protein": number,
          "carbs": number,
          "fat": number,
          "ingredients": [string],
          "instructions": [string]
        }
      ],
      "tips": [string]
    }

    Provide 4 meals (breakfast, lunch, dinner, and 1 snack). Make sure the total calories and macros add up correctly.
    `;

    try {
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }]
        })
      });

      const data = await response.json();
      const text = data.candidates[0].content.parts[0].text;
      
      // Extract JSON from response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error generating diet plan:', error);
      return this.getDefaultDietPlan(profile);
    }
  }

  async generateWorkoutPlan(profile) {
    const prompt = `
    Create a personalized workout plan for a user with the following profile:
    - Age: ${profile.age}
    - Gender: ${profile.gender}
    - Body Type: ${profile.bodyType}
    - Activity Level: ${profile.activityLevel}
    - Goal: ${profile.goal}

    Return a JSON object with the following structure:
    {
      "name": string,
      "duration": number (in minutes),
      "exercises": [
        {
          "name": string,
          "sets": number,
          "reps": string,
          "duration": number (optional, for cardio),
          "calories": number (estimated calories burned),
          "instructions": [string]
        }
      ],
      "difficulty": "beginner" | "intermediate" | "advanced"
    }

    Provide 6-8 exercises suitable for their goals and fitness level.
    `;

    try {
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }]
        })
      });

      const data = await response.json();
      const text = data.candidates[0].content.parts[0].text;
      
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error generating workout plan:', error);
      return this.getDefaultWorkoutPlan();
    }
  }

  async analyzeFoodImage(imageBase64) {
    const prompt = `
    Analyze this food image and provide nutritional information.
    Return a JSON object with:
    {
      "food": string (name of the food),
      "calories": number (estimated calories),
      "macros": {
        "protein": number (in grams),
        "carbs": number (in grams),
        "fat": number (in grams)
      }
    }
    `;

    try {
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [
              { text: prompt },
              {
                inline_data: {
                  mime_type: 'image/jpeg',
                  data: imageBase64
                }
              }
            ]
          }]
        })
      });

      const data = await response.json();
      const text = data.candidates[0].content.parts[0].text;
      
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error analyzing food image:', error);
      return {
        food: 'Unknown Food',
        calories: 200,
        macros: { protein: 10, carbs: 20, fat: 8 }
      };
    }
  }

  analyzeWeightTrend(weightHistory) {
    if (weightHistory.length < 2) return 'insufficient_data';
    
    const recent = weightHistory.slice(-4); // Last 4 entries
    const trend = recent[recent.length - 1].weight - recent[0].weight;
    
    if (trend > 1) return 'gaining';
    if (trend < -1) return 'losing';
    return 'stable';
  }

  getDefaultDietPlan(profile) {
    const baseCalories = this.calculateBMR(profile) * this.getActivityMultiplier(profile.activityLevel);
    
    return {
      dailyCalories: Math.round(baseCalories),
      macros: {
        protein: Math.round(profile.weight * 1.6),
        carbs: Math.round(baseCalories * 0.45 / 4),
        fat: Math.round(baseCalories * 0.25 / 9)
      },
      meals: [
        {
          name: 'Breakfast',
          calories: Math.round(baseCalories * 0.25),
          protein: 20,
          carbs: 40,
          fat: 15,
          ingredients: ['Oatmeal', 'Banana', 'Almonds'],
          instructions: ['Cook oatmeal', 'Add sliced banana', 'Top with almonds']
        },
        {
          name: 'Lunch',
          calories: Math.round(baseCalories * 0.35),
          protein: 30,
          carbs: 45,
          fat: 20,
          ingredients: ['Grilled Chicken', 'Brown Rice', 'Vegetables'],
          instructions: ['Grill chicken breast', 'Cook brown rice', 'Steam vegetables']
        },
        {
          name: 'Dinner',
          calories: Math.round(baseCalories * 0.30),
          protein: 25,
          carbs: 30,
          fat: 18,
          ingredients: ['Salmon', 'Sweet Potato', 'Broccoli'],
          instructions: ['Bake salmon', 'Roast sweet potato', 'Steam broccoli']
        },
        {
          name: 'Snack',
          calories: Math.round(baseCalories * 0.10),
          protein: 10,
          carbs: 15,
          fat: 8,
          ingredients: ['Greek Yogurt', 'Berries'],
          instructions: ['Mix yogurt with berries']
        }
      ],
      tips: ['Drink plenty of water', 'Eat regular meals', 'Include vegetables in every meal']
    };
  }

  getDefaultWorkoutPlan() {
    return {
      name: 'Basic Fitness Plan',
      duration: 45,
      exercises: [
        {
          name: 'Push-ups',
          sets: 3,
          reps: '10-15',
          calories: 50,
          instructions: ['Start in plank position', 'Lower body to ground', 'Push back up']
        },
        {
          name: 'Squats',
          sets: 3,
          reps: '15-20',
          calories: 60,
          instructions: ['Stand with feet shoulder-width apart', 'Lower into squat', 'Return to standing']
        },
        {
          name: 'Plank',
          sets: 3,
          reps: '30-60 seconds',
          calories: 40,
          instructions: ['Hold plank position', 'Keep body straight', 'Engage core']
        }
      ],
      difficulty: 'beginner'
    };
  }

  calculateBMR(profile) {
    // Mifflin-St Jeor Equation
    if (profile.gender === 'male') {
      return 10 * profile.weight + 6.25 * profile.height - 5 * profile.age + 5;
    } else {
      return 10 * profile.weight + 6.25 * profile.height - 5 * profile.age - 161;
    }
  }

  getActivityMultiplier(level) {
    const multipliers = {
      sedentary: 1.2,
      lightly_active: 1.375,
      moderately_active: 1.55,
      very_active: 1.725,
      extremely_active: 1.9
    };
    return multipliers[level] || 1.2;
  }
}

export const geminiService = new GeminiService();
